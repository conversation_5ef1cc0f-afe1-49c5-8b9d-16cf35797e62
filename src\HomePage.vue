<template>
  <div class="path-generator">
    <div class="controls">
      <label>
        节点数量:
        <input type="range" min="3" max="50" v-model.number="nodeCount" />
        {{ nodeCount }}
      </label>
      <label class="toggle-control">
        <input type="checkbox" v-model="showControls" />
        显示控制点
      </label>
      <button @click="generatePath">重新生成路径</button>
    </div>
    <div class="canvas-container">
      <v-stage :config="{ width: canvasWidth, height: canvasHeight }">
        <v-layer>
          <!-- 背景网格 -->
          <template v-for="gx in gridCountX" :key="'gx'+gx">
            <v-line :points="[gx * gridSize, 0, gx * gridSize, canvasHeight]" stroke="#eee" :strokeWidth="1" />
          </template>
          <template v-for="gy in gridCountY" :key="'gy'+gy">
            <v-line :points="[0, gy * gridSize, canvasWidth, gy * gridSize]" stroke="#eee" :strokeWidth="1" />
          </template>

          <!-- 路径 -->
          <v-line :points="bezierPoints" :stroke="'#3498db'" :strokeWidth="3" :tension="0" :lineCap="'round'"
            :lineJoin="'round'" :bezier="true" :closed="false" />
          <!-- 节点 -->
          <v-circle v-for="(point, idx) in pathPoints" :key="'node-' + idx" :x="point[0]" :y="point[1]" :radius="5"
            fill="#e74c3c" @mouseover="hoverIndex = idx" @mouseout="hoverIndex = null" />
          <!-- 节点标签 -->
          <v-text v-for="({ point, idx }) in hoveredLabelPoints" :key="'label-' + idx" :x="point[0] + 10" :y="point[1] - 18"
            :fontSize="12" :text="`(${point[0].toFixed(0)}, ${point[1].toFixed(0)})`" fill="#333" />
          <!-- 控制点和辅助线 -->
          <template v-if="showControls">
            <v-circle v-for="(point, idx) in controlPoints" :key="'ctrl-' + idx" :x="point[0]" :y="point[1]" :radius="3"
              fill="#f39c12" />
            <v-line v-for="(segment, idx) in pathSegments" :key="'guide-' + idx" :points="[
              segment.start[0], segment.start[1],
              segment.ctrl1[0], segment.ctrl1[1],
              segment.ctrl2[0], segment.ctrl2[1],
              segment.end[0], segment.end[1]
            ]" stroke="#95a5a6" :strokeWidth="1" dash={[2,2]} lineCap="butt" lineJoin="bevel" />
          </template>
        </v-layer>
      </v-stage>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue';
import { Stage as VStage, Layer as VLayer, Line as VLine, Circle as VCircle, Text as VText } from 'vue-konva';

// 注册vue-konva组件
// const components = { 'v-stage': VStage, 'v-layer': VLayer, 'v-line': VLine, 'v-circle': VCircle, 'v-text': VText };

const amplitude = 165;
const frequency = 2.4;
const roundness = 0.4;

const nodeCount = ref(15);
const showControls = ref(false);
const canvasWidth = ref(800);
const canvasHeight = ref(400);
const hoverIndex = ref(null);

const gridSize = 40;
const gridCountX = computed(() => Math.ceil(canvasWidth.value / gridSize));
const gridCountY = computed(() => Math.ceil(canvasHeight.value / gridSize));

// 生成正弦波路径点
const generatePathPoints = () => {
  const points = [];
  const centerY = canvasHeight.value / 2;
  for (let i = 0; i < nodeCount.value; i++) {
    const x = i * (canvasWidth.value / (nodeCount.value - 1));
    const progress = (i / (nodeCount.value - 1)) * Math.PI * 2 * frequency;
    const y = centerY + Math.sin(progress) * amplitude;
    points.push([x, y]);
  }
  return points;
};

const pathPoints = ref(generatePathPoints());

const calculateControlPoints = (points) => {
  const segments = [];
  for (let i = 0; i < points.length - 1; i++) {
    const start = points[i];
    const end = points[i + 1];
    const dx = end[0] - start[0];
    const dy = end[1] - start[1];
    const distance = Math.sqrt(dx * dx + dy * dy) * roundness;
    const prevPoint = points[i - 1] || start;
    const nextPoint = points[i + 2] || end;
    const startAngle = Math.atan2(end[1] - prevPoint[1], end[0] - prevPoint[0]);
    const endAngle = Math.atan2(nextPoint[1] - start[1], nextPoint[0] - start[0]);
    const ctrl1 = [
      start[0] + Math.cos(startAngle) * distance,
      start[1] + Math.sin(startAngle) * distance
    ];
    const ctrl2 = [
      end[0] - Math.cos(endAngle) * distance,
      end[1] - Math.sin(endAngle) * distance
    ];
    segments.push({ start, ctrl1, ctrl2, end });
  }
  return segments;
};

const pathSegments = computed(() => calculateControlPoints(pathPoints.value));
const controlPoints = computed(() => pathSegments.value.flatMap(seg => [seg.ctrl1, seg.ctrl2]));

// 转换为konva贝塞尔点格式
const bezierPoints = computed(() => {
  if (pathSegments.value.length === 0) return [];
  const result = [];
  result.push(pathSegments.value[0].start[0], pathSegments.value[0].start[1]);
  for (let seg of pathSegments.value) {
    result.push(
      seg.ctrl1[0], seg.ctrl1[1],
      seg.ctrl2[0], seg.ctrl2[1],
      seg.end[0], seg.end[1]
    );
  }
  return result;
});

const generatePath = () => {
  pathPoints.value = generatePathPoints();
};

// script部分加一个计算属性
const hoveredLabelPoints = computed(() =>
  hoverIndex.value != null
    ? [{ point: pathPoints.value[hoverIndex.value], idx: hoverIndex.value }]
    : []
);

watch([nodeCount], generatePath);
</script>

<style scoped>
.path-generator {
  font-family: Arial, sans-serif;
  max-width: 800px;
  margin: 0 auto;
}

.controls {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  margin-bottom: 1rem;
  padding: 1rem;
  background: #f5f5f5;
  border-radius: 4px;
}

.controls label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.toggle-control {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.canvas-container {
  border: 1px solid #ddd;
  border-radius: 4px;
  overflow: hidden;
}
</style>