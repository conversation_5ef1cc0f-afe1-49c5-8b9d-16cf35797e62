<template>
  <div class="path-generator">
    <div class="controls">
      <label>
        节点数量:
        <input type="range" min="3" max="50" v-model.number="nodeCount" />
        {{ nodeCount }}
      </label>
      <label class="toggle-control">
        <input type="checkbox" v-model="showControls" />
        显示控制点
      </label>
      <button @click="generatePath">重新生成路径</button>
    </div>
    <div class="canvas-container">
      <v-stage :config="{ width: canvasWidth, height: canvasHeight }">
        <v-layer>
          <!-- 背景网格 -->
          <template v-for="gx in gridCountX" :key="'gx'+gx">
            <v-line :config="{ points: [gx * gridSize, 0, gx * gridSize, canvasHeight], stroke: '#eee', strokeWidth: 1 }" />
          </template>
          <template v-for="gy in gridCountY" :key="'gy'+gy">
            <v-line :config="{ points: [0, gy * gridSize, canvasWidth, gy * gridSize], stroke: '#eee', strokeWidth: 1 }" />
          </template>

          <!-- 路径段 -->
          <v-line v-for="(segment, idx) in pathSegmentsWithColors" :key="'path-' + idx"
            :config="{
              points: segment.points,
              stroke: segment.color,
              strokeWidth: 3,
              tension: 0,
              lineCap: 'round',
              lineJoin: 'round',
              bezier: true,
              closed: false
            }" />

          <!-- 节点 -->
          <v-circle v-for="(point, idx) in pathPoints" :key="'node-' + idx"
            :config="{
              x: point[0],
              y: point[1],
              radius: getNodeRadius(idx),
              fill: getNodeColor(idx),
              stroke: '#333',
              strokeWidth: 1
            }"
            @mouseover="hoverIndex = idx" @mouseout="hoverIndex = null" />

          <!-- 节点标签 -->
          <v-text v-for="({ point, idx }) in hoveredLabelPoints" :key="'label-' + idx"
            :config="{ x: point[0] + 10, y: point[1] - 18, fontSize: 12, text: `(${point[0].toFixed(0)}, ${point[1].toFixed(0)})`, fill: '#333' }" />

          <!-- 控制点和辅助线 -->
          <template v-if="showControls">
            <v-circle v-for="(point, idx) in controlPoints" :key="'ctrl-' + idx"
              :config="{ x: point[0], y: point[1], radius: 3, fill: '#f39c12' }" />
            <v-line v-for="(segment, idx) in pathSegments" :key="'guide-' + idx"
              :config="{
                points: [
                  segment.start[0], segment.start[1],
                  segment.ctrl1[0], segment.ctrl1[1],
                  segment.ctrl2[0], segment.ctrl2[1],
                  segment.end[0], segment.end[1]
                ],
                stroke: '#95a5a6',
                strokeWidth: 1,
                dash: [2,2],
                lineCap: 'butt',
                lineJoin: 'bevel'
              }" />
          </template>
        </v-layer>
      </v-stage>
    </div>

    <!-- 节点状态控制 -->
    <div class="node-controls">
      <div class="status-info">
        <div class="status-legend">
          <div class="legend-section">
            <h4>节点状态:</h4>
            <span class="legend-item">
              <span class="color-dot" style="background-color: #95a5a6;"></span>
              未进行
            </span>
            <span class="legend-item">
              <span class="color-dot" style="background-color: #f39c12;"></span>
              正在进行
            </span>
            <span class="legend-item">
              <span class="color-dot" style="background-color: #27ae60;"></span>
              已完成
            </span>
          </div>
          <div class="legend-section">
            <h4>路径颜色:</h4>
            <span class="legend-item">
              <span class="color-line" style="background-color: #9b59b6;"></span>
              已完成路径
            </span>
            <span class="legend-item">
              <span class="color-line" style="background-color: #bdc3c7;"></span>
              未完成路径
            </span>
          </div>
        </div>
        <div class="current-node-info">
          当前节点: {{ currentNodeIndex + 1 }} / {{ nodeCount }}
        </div>
      </div>
      <div class="control-buttons">
        <button @click="completeCurrentNode" :disabled="!canCompleteCurrentNode" class="complete-btn">
          完成当前节点
        </button>
        <button @click="resetNodeStates" class="reset-btn">
          重置所有状态
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue';


const amplitude = 165;
const frequency = 2.4;
const roundness = 0.4;

const nodeCount = ref(15);
const showControls = ref(false);
const canvasWidth = ref(800);
const canvasHeight = ref(400);
const hoverIndex = ref(null);

// 节点状态系统
const NODE_STATES = {
  PENDING: 'pending',     // 未进行 - 灰色
  IN_PROGRESS: 'progress', // 正在进行 - 黄色
  COMPLETED: 'completed'   // 已完成 - 绿色
};

const nodeStates = ref([]);
const currentNodeIndex = ref(0);

// 路径动画进度 (0-1)，键为路径段索引
const pathAnimationProgress = ref({});

// 节点动画状态
const nodeAnimationStates = ref({});

const gridSize = 40;
const gridCountX = computed(() => Math.ceil(canvasWidth.value / gridSize));
const gridCountY = computed(() => Math.ceil(canvasHeight.value / gridSize));

// 生成正弦波路径点
const generatePathPoints = () => {
  const points = [];
  const centerY = canvasHeight.value / 2;
  for (let i = 0; i < nodeCount.value; i++) {
    const x = i * (canvasWidth.value / (nodeCount.value - 1));
    const progress = (i / (nodeCount.value - 1)) * Math.PI * 2 * frequency;
    const y = centerY + Math.sin(progress) * amplitude;
    points.push([x, y]);
  }
  return points;
};

const pathPoints = ref(generatePathPoints());

const calculateControlPoints = (points) => {
  const segments = [];
  for (let i = 0; i < points.length - 1; i++) {
    const start = points[i];
    const end = points[i + 1];
    const dx = end[0] - start[0];
    const dy = end[1] - start[1];
    const distance = Math.sqrt(dx * dx + dy * dy) * roundness;
    const prevPoint = points[i - 1] || start;
    const nextPoint = points[i + 2] || end;
    const startAngle = Math.atan2(end[1] - prevPoint[1], end[0] - prevPoint[0]);
    const endAngle = Math.atan2(nextPoint[1] - start[1], nextPoint[0] - start[0]);
    const ctrl1 = [
      start[0] + Math.cos(startAngle) * distance,
      start[1] + Math.sin(startAngle) * distance
    ];
    const ctrl2 = [
      end[0] - Math.cos(endAngle) * distance,
      end[1] - Math.sin(endAngle) * distance
    ];
    segments.push({ start, ctrl1, ctrl2, end });
  }
  return segments;
};

const pathSegments = computed(() => calculateControlPoints(pathPoints.value));
const controlPoints = computed(() => pathSegments.value.flatMap(seg => [seg.ctrl1, seg.ctrl2]));



// 获取路径段颜色（支持动画进度）
const getPathSegmentColor = (segmentIndex) => {
  // 路径段连接节点 segmentIndex 和 segmentIndex + 1
  const startNodeState = nodeStates.value[segmentIndex];
  const animationProgress = pathAnimationProgress.value[segmentIndex] || 0;

  // 如果起始节点已完成，路径为紫色
  if (startNodeState === NODE_STATES.COMPLETED) {
    return '#9b59b6'; // 紫色
  }
  // 如果正在动画中（从灰色过渡到紫色）
  else if (animationProgress > 0) {
    // 插值计算颜色
    const grayColor = { r: 189, g: 195, b: 199 }; // #bdc3c7
    const purpleColor = { r: 155, g: 89, b: 182 }; // #9b59b6

    const r = Math.round(grayColor.r + (purpleColor.r - grayColor.r) * animationProgress);
    const g = Math.round(grayColor.g + (purpleColor.g - grayColor.g) * animationProgress);
    const b = Math.round(grayColor.b + (purpleColor.b - grayColor.b) * animationProgress);

    return `rgb(${r}, ${g}, ${b})`;
  }
  // 其他情况（正在进行或未进行），路径为灰色
  else {
    return '#bdc3c7'; // 灰色
  }
};

// 带颜色的路径段
const pathSegmentsWithColors = computed(() => {
  if (pathSegments.value.length === 0) return [];

  return pathSegments.value.map((segment, index) => {
    const points = [
      segment.start[0], segment.start[1],
      segment.ctrl1[0], segment.ctrl1[1],
      segment.ctrl2[0], segment.ctrl2[1],
      segment.end[0], segment.end[1]
    ];

    return {
      points,
      color: getPathSegmentColor(index)
    };
  });
});

const generatePath = () => {
  pathPoints.value = generatePathPoints();
  initializeNodeStates();
};

// 初始化节点状态
const initializeNodeStates = () => {
  nodeStates.value = Array(nodeCount.value).fill(NODE_STATES.PENDING);
  nodeStates.value[0] = NODE_STATES.IN_PROGRESS; // 第一个节点设为正在进行
  currentNodeIndex.value = 0;
};

// 获取节点颜色
const getNodeColor = (index) => {
  const state = nodeStates.value[index];
  switch (state) {
    case NODE_STATES.PENDING:
      return '#95a5a6'; // 灰色
    case NODE_STATES.IN_PROGRESS:
      return '#f39c12'; // 黄色
    case NODE_STATES.COMPLETED:
      return '#27ae60'; // 绿色
    default:
      return '#95a5a6';
  }
};

// 获取节点半径（当前节点更大）
const getNodeRadius = (index) => {
  const baseRadius = 6;
  const enlargedRadius = 10;
  const state = nodeStates.value[index];

  if (state === NODE_STATES.IN_PROGRESS) {
    return enlargedRadius;
  }
  return baseRadius;
};

// 完成当前节点（带动画效果）
const completeCurrentNode = () => {
  if (currentNodeIndex.value < nodeStates.value.length) {
    const currentIndex = currentNodeIndex.value;

    // 将当前节点标记为已完成
    nodeStates.value[currentIndex] = NODE_STATES.COMPLETED;

    // 启动路径颜色动画（如果有前一个路径段）
    if (currentIndex > 0) {
      const pathSegmentIndex = currentIndex - 1;
      animatePathColor(pathSegmentIndex);
    }

    // 移动到下一个节点
    currentNodeIndex.value++;

    // 如果还有下一个节点，将其设为正在进行（带动画效果）
    if (currentNodeIndex.value < nodeStates.value.length) {
      setTimeout(() => {
        nodeStates.value[currentNodeIndex.value] = NODE_STATES.IN_PROGRESS;
      }, 300); // 延迟一点让路径动画先开始
    }
  }
};

// 路径颜色动画
const animatePathColor = (segmentIndex) => {
  const duration = 800; // 动画持续时间
  const startTime = Date.now();

  const animate = () => {
    const elapsed = Date.now() - startTime;
    const progress = Math.min(elapsed / duration, 1);

    // 使用缓动函数
    const easeProgress = 1 - Math.pow(1 - progress, 3);

    pathAnimationProgress.value = {
      ...pathAnimationProgress.value,
      [segmentIndex]: easeProgress
    };

    if (progress < 1) {
      requestAnimationFrame(animate);
    }
  };

  requestAnimationFrame(animate);
};

// 重置所有节点状态
const resetNodeStates = () => {
  initializeNodeStates();
};

// 检查是否可以完成当前节点
const canCompleteCurrentNode = computed(() => {
  return currentNodeIndex.value < nodeStates.value.length &&
         nodeStates.value[currentNodeIndex.value] === NODE_STATES.IN_PROGRESS;
});

// script部分加一个计算属性
const hoveredLabelPoints = computed(() =>
  hoverIndex.value != null
    ? [{ point: pathPoints.value[hoverIndex.value], idx: hoverIndex.value }]
    : []
);

watch([nodeCount], generatePath);

// 初始化
initializeNodeStates();
</script>

<style scoped>
.path-generator {
  font-family: Arial, sans-serif;
  max-width: 800px;
  margin: 0 auto;
}

.controls {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  margin-bottom: 1rem;
  padding: 1rem;
  background: #f5f5f5;
  border-radius: 4px;
}

.controls label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.toggle-control {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.canvas-container {
  border: 1px solid #ddd;
  border-radius: 4px;
  overflow: hidden;
}

.node-controls {
  margin-top: 1rem;
  padding: 1rem;
  background: #f8f9fa;
  border-radius: 4px;
  border: 1px solid #e9ecef;
}

.status-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  flex-wrap: wrap;
  gap: 1rem;
}

.status-legend {
  display: flex;
  gap: 2rem;
  flex-wrap: wrap;
  align-items: flex-start;
}

.legend-section {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.legend-section h4 {
  margin: 0;
  font-size: 0.9rem;
  color: #2c3e50;
  font-weight: bold;
}

.legend-section .legend-item {
  margin-left: 0.5rem;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.85rem;
}

.color-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  border: 1px solid #333;
}

.color-line {
  width: 20px;
  height: 3px;
  border-radius: 2px;
  border: 1px solid #333;
}

.current-node-info {
  font-weight: bold;
  color: #2c3e50;
}

.control-buttons {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.complete-btn, .reset-btn {
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: all 0.2s ease;
}

.complete-btn {
  background-color: #27ae60;
  color: white;
}

.complete-btn:hover:not(:disabled) {
  background-color: #219a52;
}

.complete-btn:disabled {
  background-color: #bdc3c7;
  cursor: not-allowed;
}

.reset-btn {
  background-color: #e74c3c;
  color: white;
}

.reset-btn:hover {
  background-color: #c0392b;
}
</style>